import { SidebarGroup, SidebarGroupLabel, SidebarMenu, SidebarMenuButton, SidebarMenuItem, useSidebar } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link, usePage } from '@inertiajs/react';

type NavMainProps = {
    items?: NavItem[];
    title?: string;
};

export function NavMain({ items = [], title = '' }: NavMainProps) {
    const page = usePage();
    const { isMobile, setOpen , open} = useSidebar();

    const handleLinkClick = () => {
        // Auto-close sidebar on mobile when clicking navigation links
        if (isMobile) {
            setOpen(false);
        }
    };

    return (
        <SidebarGroup className="px-2 py-0">
            {(title && open) && <SidebarGroupLabel>{title}</SidebarGroupLabel>}
            <SidebarMenu>
                {items.map((item) => (
                    <SidebarMenuItem key={item.title}>
                        <SidebarMenuButton asChild isActive={page.url == item.href} tooltip={{ children: item.title }}>
                            <Link href={item.href} prefetch onClick={handleLinkClick}>
                                {item.icon && <item.icon />}
                                <span>{item.title}</span>
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                ))}
            </SidebarMenu>
        </SidebarGroup>
    );
}
