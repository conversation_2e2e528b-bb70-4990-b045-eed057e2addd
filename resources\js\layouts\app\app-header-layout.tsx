import { AppContent } from '@/components/app-content';
// import { AppHeader } from '@/components/app-header';
import { AppShell } from '@/components/app-shell';
import { type BreadcrumbItem } from '@/types';
import type { PropsWithChildren } from 'react';
import Footer from '../../components/footer';
import Navbar from '../../components/navbar';

export default function AppHeaderLayout({ children }: PropsWithChildren<{ breadcrumbs?: BreadcrumbItem[] }>) {
    return (
        <AppShell>
            {/* <AppHeader breadcrumbs={breadcrumbs} /> */}
            <Navbar />
            <AppContent>{children}</AppContent>
            <Footer />
        </AppShell>
    );
}
