@import 'tailwindcss';

@plugin 'tailwindcss-animate';

@source '../views';
@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';

@custom-variant dark (&:is(.dark *));

@theme {
    --font-sans:
        'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

    --color-alpha: #fee819;
    --color-beta: #212529;
    --color-error: #ff7376;
    --color-good: #51b04f;
    --color-light_gray: #f2f2f2;
    --color-skeleton1: #cacaca;
    --color-skeleton2: #a3a3a3;
    --radius-lg: var(--radius);
    --radius-md: calc(var(--radius) - 2px);
    --radius-sm: calc(var(--radius) - 4px);

    --color-background: var(--background);
    --color-foreground: var(--foreground);

    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);

    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);

    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);

    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);

    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);

    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);

    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);

    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);

    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);

    --color-sidebar: #121212;
    --color-sidebar-foreground: #ffffff;
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: #fee819;
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentColor);
    }
}

:root {
    --background: oklch(1 0 0);
    --foreground: oklch(0.145 0 0);
    --card: oklch(1 0 0);
    --card-foreground: oklch(0.145 0 0);
    --popover: oklch(1 0 0);
    --popover-foreground: oklch(0.145 0 0);
    --primary: oklch(0.205 0 0);
    --primary-foreground: oklch(0.985 0 0);
    --secondary: oklch(0.97 0 0);
    --secondary-foreground: oklch(0.205 0 0);
    --muted: oklch(0.97 0 0);
    --muted-foreground: oklch(0.556 0 0);
    --accent: oklch(0.97 0 0);
    --accent-foreground: oklch(0.205 0 0);
    --destructive: oklch(0.577 0.245 27.325);
    --destructive-foreground: oklch(0.577 0.245 27.325);
    --border: oklch(0.922 0 0);
    --input: oklch(0.922 0 0);
    --ring: oklch(0.87 0 0);
    --chart-1: oklch(0.646 0.222 41.116);
    --chart-2: oklch(0.6 0.118 184.704);
    --chart-3: oklch(0.398 0.07 227.392);
    --chart-4: oklch(0.828 0.189 84.429);
    --chart-5: oklch(0.769 0.188 70.08);
    --radius: 0.625rem;
    --sidebar: oklch(0.985 0 0);
    --sidebar-foreground: oklch(0.145 0 0);
    --sidebar-primary: oklch(0.205 0 0);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.97 0 0);
    --sidebar-accent-foreground: oklch(0.205 0 0);
    --sidebar-border: oklch(0.922 0 0);
    --sidebar-ring: oklch(0.87 0 0);
}

/*
.dark {
    --background: oklch(0.145 0 0);
    --foreground: oklch(0.985 0 0);
    --card: oklch(0.145 0 0);
    --card-foreground: oklch(0.985 0 0);
    --popover: oklch(0.145 0 0);
    --popover-foreground: oklch(0.985 0 0);
    --primary: oklch(0.985 0 0);
    --primary-foreground: oklch(0.205 0 0);
    --secondary: oklch(0.269 0 0);
    --secondary-foreground: oklch(0.985 0 0);
    --muted: oklch(0.269 0 0);
    --muted-foreground: oklch(0.708 0 0);
    --accent: oklch(0.269 0 0);
    --accent-foreground: oklch(0.985 0 0);
    --destructive: oklch(0.396 0.141 25.723);
    --destructive-foreground: oklch(0.637 0.237 25.331);
    --border: oklch(0.269 0 0);
    --input: oklch(0.269 0 0);
    --ring: oklch(0.439 0 0);
    --chart-1: oklch(0.488 0.243 264.376);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);
    --sidebar: oklch(0.205 0 0);
    --sidebar-foreground: oklch(0.985 0 0);
    --sidebar-primary: oklch(0.985 0 0);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.269 0 0);
    --sidebar-accent-foreground: oklch(0.985 0 0);
    --sidebar-border: oklch(0.269 0 0);
    --sidebar-ring: oklch(0.439 0 0);
} */

@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply bg-background text-foreground;
    }
}

/* To Reset Tailwind Classes in a div and it's children */

.reset-tw {
    all: revert;
    box-sizing: border-box;
    word-break: break-word;
    white-space: pre-wrap;
    overflow-wrap: break-word;
    width: 100%;
    min-height: 200px;
    outline: none;
    margin: 0;
    line-height: 1.5;
}

.reset-tw p:is(:first-child:empty)::before {
    color: #aaa;
    outline: none;
    pointer-events: none;
}

.fl-flasher.fl-container.fl-show {
    z-index: 100;
}

.bg-image {
    background-image: url('../assets/images/gallery/image.png');
    /* background-position: center; */
}

.bg-image-coding {
    background-image: url('../assets/images/gallery/coding.jpg');
    background-position: bottom;
}

.bg-image-media {
    background-image: url('../assets/images/gallery/media.jpg');
}

/* Text truncation utility */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Skeleton container */
.skeleton {
    background: #ddd;
    /* Base gray background */
    position: relative;
    overflow: hidden;
}

/* Skeleton animation */
.skeleton::after {
    content: '';
    position: absolute;
    top: 0;
    left: -150%;
    /* Start off-screen */
    width: 150%;
    /* Larger than the container to sweep across */
    height: 100%;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 50%, rgba(255, 255, 255, 0) 100%);
    animation: loading 1.5s infinite;
    /* Use the animation keyframes */
}

@keyframes loading {
    0% {
        left: -150%;
        /* Start position */
    }

    100% {
        left: 100%;
        /* End position */
    }
}

.no-scrollbar {
    scrollbar-width: none;
    /* Firefox */
    -ms-overflow-style: none;
    /* Internet Explorer 10+ */
}

.no-scrollbar::-webkit-scrollbar {
    display: none;
    /* Safari and Chrome */
}

.projects {
    transition: 0.5s ease;
}

.preview {
    position: absolute;
    top: 120vh;
    left: 0;
    transition: 0.5s ease;
}

.projects:hover > .preview {
    top: 0;
    transition: 0.5s ease;
}

@media (max-width: 640px) {
    .flex-\[calc\(calc\(100\%-calc\(2\*3\.5rem\)\)\/3\)\] {
        flex: 1 0 100%;
    }
}

input[type='checkbox'] {
    width: 15px;
    height: 15px;
    accent-color: #fee819;
}

input[type='radio'] {
    accent-color: black;
}

.codingformationpic {
    position: relative;
    overflow: hidden;
    height: 100%;
    border-top-left-radius: inherit;
    border-bottom-left-radius: inherit;
}

.codingformationpic img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease-in-out;
}

.codingformationpic img:hover {
    transform: scale(1.05);
    /* Smooth zoom effect on hover */
}

.flipped-svg {
    transform: scaleX(-1);
    /* Flip the SVG horizontally */
    transition: fill 0.3s ease-in-out;
    /* Smooth transition for color changes */
}

.flipped-svg:hover {
    fill: #e0e0e0;
    /* Change color on hover for a smoother effect */
}

.codingformationpic1 svg {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
}

/* Custom scrollbar styling */
.custom-scrollbar::-webkit-scrollbar {
    width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
    transition: background 0.3s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
}

/* For Firefox */
.custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;
}
body {
  pointer-events: auto !important;
}

body:not(.modal-open) {
  overflow: auto !important;
  pointer-events: auto !important;
}
