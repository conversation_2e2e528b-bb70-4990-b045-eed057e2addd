
[2025-09-22 10:49:25] local.ERROR: The payload is invalid. {"exception":"[object] (Illuminate\\Contracts\\Encryption\\DecryptException(code: 0): The payload is invalid. at C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\Encrypter.php:244)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\Encrypter.php(157): Illuminate\\Encryption\\Encrypter->getJsonPayload(NULL)
#1 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\Encrypter.php(208): Illuminate\\Encryption\\Encrypter->decrypt('test123', false)
#2 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(363): Illuminate\\Encryption\\Encrypter->decryptString('test123')
#3 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\app\\Http\\Controllers\\ParticipantController.php(1021): Illuminate\\Support\\Facades\\Facade::__callStatic('decryptString', Array)
#4 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\ParticipantController->confirmationSchool('Test User', 'test123')
#5 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ParticipantController), 'confirmationSch...')
#6 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#7 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#8 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():821}(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\php-flasher\\flasher-laravel\\Middleware\\FlasherMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Flasher\\Laravel\\Middleware\\FlasherMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\php-flasher\\flasher-laravel\\Middleware\\SessionMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Flasher\\Laravel\\Middleware\\SessionMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(96): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#36 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#63 {main}
"} 
[2025-09-22 11:04:08] local.ERROR: Too many arguments to "queue:work" command, expected arguments "connection". {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): Too many arguments to \"queue:work\" command, expected arguments \"connection\". at C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Input\\ArgvInput.php:199)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Input\\ArgvInput.php(92): Symfony\\Component\\Console\\Input\\ArgvInput->parseArgument('==Coding')
#1 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Input\\ArgvInput.php(77): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('==Coding', true)
#2 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Input\\Input.php(53): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#3 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Command\\Command.php(276): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#4 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#5 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Application.php(1110): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Application.php(359): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Application.php(194): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#11 {main}
"} 
[2025-09-22 11:04:14] local.ERROR: The [queue==Coding] queue connection has not been configured. {"exception":"[object] (InvalidArgumentException(code: 0): The [queue==Coding] queue connection has not been configured. at C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\QueueManager.php:169)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\QueueManager.php(148): Illuminate\\Queue\\QueueManager->resolve('queue==Coding')
#1 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(174): Illuminate\\Queue\\QueueManager->connection('queue==Coding')
#2 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->daemon('queue==Coding', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#3 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('queue==Coding', 'default')
#4 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#5 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#6 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(836): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#9 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#10 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#11 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#12 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Application.php(1110): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Application.php(359): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Application.php(194): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#18 {main}
"} 
[2025-09-22 11:25:29] local.ERROR: Mailer [Coding] is not defined. {"userId":11,"exception":"[object] (InvalidArgumentException(code: 0): Mailer [Coding] is not defined. at C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\MailManager.php:116)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\MailManager.php(100): Illuminate\\Mail\\MailManager->resolve('Coding')
#1 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\MailManager.php(78): Illuminate\\Mail\\MailManager->get('Coding')
#2 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(363): Illuminate\\Mail\\MailManager->mailer('Coding')
#3 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\app\\Http\\Controllers\\ParticipantController.php(952): Illuminate\\Support\\Facades\\Facade::__callStatic('mailer', Array)
#4 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\ParticipantController->toJungle(Object(Illuminate\\Http\\Request))
#5 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ParticipantController), 'toJungle')
#6 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#7 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#8 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():821}(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\php-flasher\\flasher-laravel\\Middleware\\FlasherMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Flasher\\Laravel\\Middleware\\FlasherMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\php-flasher\\flasher-laravel\\Middleware\\SessionMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Flasher\\Laravel\\Middleware\\SessionMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(96): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():194}:195}(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#63 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#66 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#67 {main}
"} 
[2025-09-22 11:39:26] production.ERROR: Command "queue:manage" is not defined.

Did you mean one of these?
    queue:batches-table
    queue:clear
    queue:failed
    queue:failed-table
    queue:flush
    queue:forget
    queue:listen
    queue:monitor
    queue:prune-batches
    queue:prune-failed
    queue:restart
    queue:retry
    queue:retry-batch
    queue:table
    queue:work {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"queue:manage\" is not defined.

Did you mean one of these?
    queue:batches-table
    queue:clear
    queue:failed
    queue:failed-table
    queue:flush
    queue:forget
    queue:listen
    queue:monitor
    queue:prune-batches
    queue:prune-failed
    queue:restart
    queue:retry
    queue:retry-batch
    queue:table
    queue:work at C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Application.php:743)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->find('queue:manage')
#1 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Application.php(194): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#5 {main}
"} 
[2025-09-22 11:54:32] production.ERROR: Expected response code "250/251/252" but got code "550", with message "550-The mail server could not deliver <NAME_EMAIL>.  The account or
550-domain may not exist, they may be blacklisted, or missing the proper dns
550 entries.". {"exception":"[object] (Symfony\\Component\\Mailer\\Exception\\UnexpectedResponseException(code: 550): Expected response code \"250/251/252\" but got code \"550\", with message \"550-The mail server could not deliver <NAME_EMAIL>.  The account or

550-domain may not exist, they may be blacklisted, or missing the proper dns

550 entries.\". at C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php:342)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(198): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->assertResponseCode('550-The mail se...', Array)
#1 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\mailer\\Transport\\Smtp\\EsmtpTransport.php(150): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->executeCommand('RCPT TO:<test@e...', Array)
#2 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(268): Symfony\\Component\\Mailer\\Transport\\Smtp\\EsmtpTransport->executeCommand('RCPT TO:<test@e...', Array)
#3 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(217): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doRcptToCommand('<EMAIL>...')
#4 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\mailer\\Transport\\AbstractTransport.php(69): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->doSend(Object(Symfony\\Component\\Mailer\\SentMessage))
#5 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\mailer\\Transport\\Smtp\\SmtpTransport.php(138): Symfony\\Component\\Mailer\\Transport\\AbstractTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#6 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(584): Symfony\\Component\\Mailer\\Transport\\Smtp\\SmtpTransport->send(Object(Symfony\\Component\\Mime\\Email), Object(Symfony\\Component\\Mailer\\DelayedEnvelope))
#7 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php(331): Illuminate\\Mail\\Mailer->sendSymfonyMessage(Object(Symfony\\Component\\Mime\\Email))
#8 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailable.php(207): Illuminate\\Mail\\Mailer->send('maizzlMails.jun...', Array, Object(Closure))
#9 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\Localizable.php(19): Illuminate\\Mail\\Mailable->{closure:Illuminate\\Mail\\Mailable::send():200}()
#10 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailable.php(200): Illuminate\\Mail\\Mailable->withLocale(NULL, Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\SendQueuedMailable.php(82): Illuminate\\Mail\\Mailable->send(Object(Illuminate\\Mail\\MailManager))
#12 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Mail\\SendQueuedMailable->handle(Object(Illuminate\\Mail\\MailManager))
#13 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#14 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#16 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(836): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#17 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(132): Illuminate\\Container\\Container->call(Array)
#18 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Bus\\Dispatcher->{closure:Illuminate\\Bus\\Dispatcher::dispatchNow():129}(Object(Illuminate\\Mail\\SendQueuedMailable))
#19 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}(Object(Illuminate\\Mail\\SendQueuedMailable))
#20 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(136): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(134): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(Illuminate\\Mail\\SendQueuedMailable), false)
#22 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Queue\\CallQueuedHandler->{closure:Illuminate\\Queue\\CallQueuedHandler::dispatchThroughMiddleware():127}(Object(Illuminate\\Mail\\SendQueuedMailable))
#23 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():178}(Object(Illuminate\\Mail\\SendQueuedMailable))
#24 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(127): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(68): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Mail\\SendQueuedMailable))
#26 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#27 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(451): Illuminate\\Queue\\Jobs\\Job->fire()
#28 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(401): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#29 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(344): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#30 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#31 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#32 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#33 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#34 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(836): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#37 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#38 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#39 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#40 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Application.php(1110): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Application.php(359): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Application.php(194): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#46 {main}
"} 
[2025-09-22 12:05:44] production.ERROR: The [once] queue connection has not been configured. {"exception":"[object] (InvalidArgumentException(code: 0): The [once] queue connection has not been configured. at C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\QueueManager.php:169)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\QueueManager.php(148): Illuminate\\Queue\\QueueManager->resolve('once')
#1 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(174): Illuminate\\Queue\\QueueManager->connection('once')
#2 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->daemon('once', 'coding', Object(Illuminate\\Queue\\WorkerOptions))
#3 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('once', 'coding')
#4 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#5 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#6 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(836): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#9 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#10 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#11 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#12 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Application.php(1110): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Application.php(359): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Application.php(194): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#18 {main}
"} 
[2025-09-22 12:31:52] local.ERROR: The "--table" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--table\" option does not exist. at C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Input\\ArgvInput.php:226)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Input\\ArgvInput.php(153): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('table', 'jobs')
#1 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Input\\ArgvInput.php(88): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--table=jobs')
#2 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Input\\ArgvInput.php(77): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--table=jobs', true)
#3 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Input\\Input.php(53): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Command\\Command.php(276): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Application.php(1110): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Application.php(359): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\ShowCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Application.php(194): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 12:31:59] local.ERROR: TTY mode is not supported on Windows platform. {"exception":"[object] (Symfony\\Component\\Process\\Exception\\RuntimeException(code: 0): TTY mode is not supported on Windows platform. at C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\process\\Process.php:1044)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\DbCommand.php(53): Symfony\\Component\\Process\\Process->setTty(true)
#1 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\DbCommand->handle()
#2 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#3 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(836): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#6 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#7 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#8 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#9 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Application.php(1110): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Application.php(359): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\DbCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Application.php(194): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#15 {main}
"} 
[2025-09-22 12:33:15] production.INFO: Jungle email queued for participant 597 (<EMAIL>)  
[2025-09-22 12:33:15] production.INFO: Jungle email queued for participant 601 (<EMAIL>)  
[2025-09-22 12:34:27] local.ERROR: Not enough arguments (missing: "queues"). {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): Not enough arguments (missing: \"queues\"). at C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Input\\Input.php:69)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Command\\Command.php(312): Symfony\\Component\\Console\\Input\\Input->validate()
#1 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#2 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Application.php(1110): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Application.php(359): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\MonitorCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\symfony\\console\\Application.php(194): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\Users\\<USER>\\Desktop\\lionsgeek-app\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#8 {main}
"} 
