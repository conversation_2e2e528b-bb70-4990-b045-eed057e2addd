<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('participants', function (Blueprint $table) {
            //
                        $table->string('intelligence_level')->nullable()->after('time_spent_formatted');
                        
                    });
                }
                
                /**
                 * Reverse the migrations.
                */
                public function down(): void
                {
                    Schema::table('participants', function (Blueprint $table) {
                        //
            
        });
    }
};
